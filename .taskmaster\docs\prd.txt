Project: Market Account Key - Automated Digital Account Marketplace

Objective: As a system administrator and MMO operator, I want to manage, sell, and deliver digital accounts or licenses (e.g., Netflix, Facebook, JetBrains) through an automated platform.

Core Components:
1.  **Client-Side Marketplace (Frontend Project: `client`)**:
    -   Users can browse a catalog of available digital accounts and licenses.
    -   Users can select products, add them to a cart, and proceed to checkout.
    -   Users can complete purchases.
    -   Upon successful purchase, users receive their account details or license keys instantly.
    -   Users should have a personal dashboard to view their purchase history and access their products.

2.  **Admin Dashboard (Frontend Project: `admin`)**:
    -   <PERSON><PERSON> can manage the product inventory (add, remove, update accounts/licenses).
    -   <PERSON><PERSON> can view and manage incoming orders.
    -   <PERSON><PERSON> can manage user accounts (view, suspend, etc.).
    -   <PERSON><PERSON> can configure and monitor the automation logic for account delivery.
    -   Dashboard should display key metrics (sales, inventory levels, etc.).

3.  **Backend Service**:
    -   Acts as the central API bridging the frontends and the database.
    -   Handles all business logic: user authentication, order processing, inventory management, and payment gateway integration.
    -   Provides secure endpoints for both the `client` and `admin` applications.

4.  **Integrations**:
    -   **Email Service**: For sending order confirmations, account details, and notifications.
    -   **OTP Service**: For user authentication and secure transactions.
    -   **License Key Generators**: Interface with external or internal tools to generate license keys on-demand if applicable.

Technology Stack:
-   **Backend Framework**: Bun (using TypeScript)
-   **Database**: Supabase (PostgreSQL)
-   **Frontend Projects**: Two separate projects are required:
    -   `client`: The customer-facing marketplace.
    -   `admin`: The internal management dashboard.
-   **Data Storage**: All data, including users, products, inventory, and orders, will be stored in Supabase.

Architectural Notes:
- The Bun backend will be the sole intermediary between the frontend applications and the Supabase database. Direct database access from the clients is not permitted.
- The system must be designed for scalability and security, handling sensitive account information appropriately.